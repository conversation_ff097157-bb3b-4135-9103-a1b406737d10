const express = require('express');
const { createProxyMiddleware } = require('http-proxy-middleware');
const cors = require('cors');

const app = express();
const PORT = 3000;

// Configurar CORS manualmente para máxima compatibilidade
app.use((req, res, next) => {
  const origin = req.headers.origin;

  // Permitir origens específicas ou localhost
  if (origin && (
    origin.includes('localhost') ||
    origin.includes('127.0.0.1') ||
    origin.includes('***********') ||
    origin.includes('smartv.shop')
  )) {
    res.header('Access-Control-Allow-Origin', origin);
  } else {
    res.header('Access-Control-Allow-Origin', '*');
  }

  res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With, Accept, Origin');
  res.header('Access-Control-Allow-Credentials', 'true');

  // Handle preflight requests
  if (req.method === 'OPTIONS') {
    res.sendStatus(200);
  } else {
    next();
  }
});

// Middleware para logs
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.url} from ${req.ip}`);
  next();
});

// Middleware para parsing JSON
app.use(express.json());

// Rota de teste IPTV
app.post('/test/request', (req, res) => {
  console.log('📺 Gerando teste IPTV:', req.body);

  const { name, email } = req.body;

  // Gerar credenciais aleatórias
  const user = Math.floor(Math.random() * 90000000) + 10000000;
  const password = Math.floor(Math.random() * 90000000) + 10000000;

  // Data de expiração (24 horas)
  const expirationDate = new Date();
  expirationDate.setHours(expirationDate.getHours() + 24);

  // Formato compatível com a interface TestResponse
  const testData = {
    success: true,
    message: "Teste liberado com sucesso! Acesso válido por 24 horas.",
    testUrl: "http://cs.tvapp.shop:80",
    credentials: {
      username: user.toString(),
      password: password.toString()
    },
    accessDetails: {
      code: user.toString(),
      dnsStb: "cs.tvapp.shop",
      urlXciptv: [`http://cs.tvapp.shop:80/get.php?username=${user}&password=${password}&type=m3u_plus&output=mpegts`],
      linkM3u: `http://cs.tvapp.shop:80/get.php?username=${user}&password=${password}&type=m3u_plus&output=mpegts`,
      linkM3uShort: `http://e.cs.tvapp.shop/p/${user}/${password}/m3u`,
      linkHls: `http://cs.tvapp.shop:80/get.php?username=${user}&password=${password}&type=m3u_plus&output=hls`,
      linkHlsShort: `http://e.cs.tvapp.shop/p/${user}/${password}/hls`,
      linkSsiptv: `http://e.cs.tvapp.shop/p/${user}/${password}/ssiptv`,
      webPlayers: [`http://cs.tvapp.shop:80/player/${user}/${password}`],
      iptvStream: `http://cs.tvapp.shop:80/live/${user}/${password}`,
      expiresAt: expirationDate.toLocaleString('pt-BR'),
      connections: 1,
      planName: "Teste Grátis 24h",
      price: "Grátis",
      createdAt: new Date().toLocaleString('pt-BR'),
      renewalUrl: "https://smartv-iptv.com/assinar"
    },
    // Manter compatibilidade com o formato antigo
    user: user.toString(),
    password: password.toString(),
    expirationDate: expirationDate.toLocaleString('pt-BR'),
    urls: {
      m3u: `http://cs.tvapp.shop:80/get.php?username=${user}&password=${password}&type=m3u_plus&output=mpegts`,
      hls: `http://cs.tvapp.shop:80/get.php?username=${user}&password=${password}&type=m3u_plus&output=hls`,
      short_m3u: `http://e.cs.tvapp.shop/p/${user}/${password}/m3u`,
      short_hls: `http://e.cs.tvapp.shop/p/${user}/${password}/hls`,
      ssiptv: `http://e.cs.tvapp.shop/p/${user}/${password}/ssiptv`
    },
    server: {
      host: 'cs.tvapp.shop',
      port: '80'
    }
  };

  console.log('✅ Teste gerado:', testData);
  res.json(testData);
});

// Rota de pagamento PIX
app.post('/payment/pix', (req, res) => {
  console.log('💰 Gerando PIX:', req.body);

  const { amount, description, customerName, planType } = req.body;

  // Gerar um código PIX simulado mais realista
  const pixCode = `00020126580014br.gov.bcb.pix0136${Math.random().toString(36).substr(2, 32)}520400005303986540${amount.toFixed(2)}5802BR5925${customerName || 'SmartV IPTV'}6009SAO PAULO62070503***6304${Math.random().toString(36).substr(2, 4).toUpperCase()}`;

  // Gerar uma imagem de QR Code placeholder mais realista (200x200 pixels)
  const qrCodeBase64 = generateQRCodePlaceholder();

  // Simular resposta do Mercado Pago no formato correto
  const pixData = {
    id: Math.random().toString(36).substr(2, 9),
    status: 'pending',
    qrCode: pixCode,
    qrCodeUrl: `data:image/png;base64,${qrCodeBase64}`,
    pixKey: pixCode,
    amount: amount,
    transactionAmount: amount,
    description: description || `SmartV IPTV - ${planType || 'Plano Selecionado'}`,
    expiresAt: new Date(Date.now() + 15 * 60 * 1000).toISOString() // 15 minutos
  };

  console.log('✅ PIX gerado:', pixData);
  res.json(pixData);
});

// Rota de health check
app.get('/health', (req, res) => {
  res.json({ status: 'ok', timestamp: new Date().toISOString() });
});

// Proxy para o Encore backend (fallback)
app.use('/', createProxyMiddleware({
  target: 'http://localhost:4000',
  changeOrigin: true,
  logLevel: 'debug',
  onError: (err, req, res) => {
    console.error('Proxy error:', err);
    res.status(500).json({ error: 'Backend não disponível' });
  },
  onProxyReq: (proxyReq, req, res) => {
    console.log('Proxying request to:', proxyReq.path);
  }
}));

const server = app.listen(PORT, '0.0.0.0', () => {
  console.log(`🚀 Proxy server rodando em http://0.0.0.0:${PORT}`);
  console.log(`📱 Acesse do mobile: http://***********:${PORT}`);
  console.log(`🔗 Proxying para: http://localhost:4000`);
  console.log('✅ Servidor iniciado com sucesso!');
});

server.on('error', (err) => {
  console.error('❌ Erro no servidor:', err);
});

// Manter o processo vivo
process.on('SIGINT', () => {
  console.log('\n👋 Proxy server sendo encerrado...');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n👋 Proxy server sendo encerrado...');
  process.exit(0);
});
