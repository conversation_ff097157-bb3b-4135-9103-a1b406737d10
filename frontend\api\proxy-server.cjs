const express = require('express');
const { createProxyMiddleware } = require('http-proxy-middleware');
const cors = require('cors');

const app = express();
const PORT = 3000;

// Configurar CORS manualmente para máxima compatibilidade
app.use((req, res, next) => {
  const origin = req.headers.origin;

  // Permitir origens específicas ou localhost
  if (origin && (
    origin.includes('localhost') ||
    origin.includes('127.0.0.1') ||
    origin.includes('***********') ||
    origin.includes('smartv.shop')
  )) {
    res.header('Access-Control-Allow-Origin', origin);
  } else {
    res.header('Access-Control-Allow-Origin', '*');
  }

  res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With, Accept, Origin');
  res.header('Access-Control-Allow-Credentials', 'true');

  // Handle preflight requests
  if (req.method === 'OPTIONS') {
    res.sendStatus(200);
  } else {
    next();
  }
});

// Middleware para logs
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.url} from ${req.ip}`);
  next();
});

// Middleware para parsing JSON
app.use(express.json());

// Rota de teste IPTV
app.post('/test/request', (req, res) => {
  console.log('📺 Gerando teste IPTV:', req.body);

  const { name, email } = req.body;

  // Gerar credenciais aleatórias
  const user = Math.floor(Math.random() * 90000000) + 10000000;
  const password = Math.floor(Math.random() * 90000000) + 10000000;

  // Data de expiração (24 horas)
  const expirationDate = new Date();
  expirationDate.setHours(expirationDate.getHours() + 24);

  // Gerar resposta completa formatada como no padrão CINE FLIX
  const createdDate = new Date().toLocaleString('pt-BR');
  const expiresDate = expirationDate.toLocaleString('pt-BR');

  const fullFormattedResponse = `〽 CINE FLIX PRO 〽🔴
━━━━━━━━━━━━━━━━━━━⚡  ⚡

🔑 Usuário: ${user}
🔒 Senha: ${password}
📆 Plano: 👽TESTE IPTV S/ ADULTOS 👽 PRO

━━━━━━━━━━━━━━━━━━━
💳 Assinar/Renovar Plano: https://cineflx.topbrr.shop/#/checkout/RXDgQzY1ex/MeWew2A8Ln
💸 Preço do Plano: R$ 0,00
🗓 Criado em: ${createdDate}
🗓 Vencimento: ${expiresDate}
📶 Conexões: 1

━━━━━━━━━━━━━━━━━━━🔴⚡
🛡️ DNS XCIPTV: http://cdn.dns7b.site
🛡️ DNS SMARTERS: http://cdn.dns7b.site
🛡️ DNS OP2: http://cdn.mp77.site
🛡️ DNS http://pro.cnfl.xyz
 🛡️ DNS  http://x25.axpl.shop ✅(nova)🚨100%
━━━━━━━━━━━━━━━━━━━
⚡ PARCERIA XCLOUD TV ⚡

🔴 PROVEDOR: CINEX
🔴 PROVEDOR: N1020
━━━━━━━━━━━━━━━━━━━⚡  🔴
💜ASSIST  CODE : 634821

🟢 PARCERIAS 🆔 1197:  ROKU

🟢ID DE PARCERIA :1197  INIFINITY PLAY E POP SMART
ROKU SANSUNG LG

🟢App HDX (ROKU e LG )
➡️ Use o ID: 112323
━━━━━━━━━━━━━━━━━━━OP1⚡ 🔴
🛢️ Links M3U:

🟢 M3U: http://cdn.dns7b.site/get.php?username=${user}&password=${password}&type=m3u_plus&output=mpegts

🟡 HLS: http://cdn.dns7b.site/get.php?username=${user}&password=${password}&type=m3u_plus&output=hls

━━━━━━━━━━━━━━━━━━━
🟢 M3U Curto: http://e.cdn.dns7b.site/p/${user}/${password}/m3u

🟡 Links HLS:

🟡 HLS: http://cdn.dns7b.site/get.php?username=${user}&password=${password}&type=m3u_plus&output=hls

🟡 HLS Curto: http://e.cdn.dns7b.site/p/${user}/${password}/hls

🔴 SSIPTV:
🔴 http://e.cdn.dns7b.site/p/${user}/${password}/ssiptv
━━━━━━━━━━━━━━━━━━━OP2⚡  🔴
🌎ROTA ALTERNATIVA

🛡️ DNS http://cdn.mp77.site

🟢 M3U:   http://cdn.mp77.site/get.php?username=${user}&password=${password}&type=m3u_plus&output=mpegts

🟡 HLS OP2:   http://cdn.mp77.site/get.php?username=${user}&password=${password}&type=m3u_plus&output=hls
━━━━━━━━━━━━━━━━━━━OP3⚡🔴
🟢 M3U:    http://pro.cnfl.xyz/get.php?username=${user}&password=${password}&type=m3u_plus&output=mpegts

🟡 HLS OP2:    http://pro.cnfl.xyz/get.php?username=${user}&password=${password}&type=m3u_plus&output=hls
━━━━━━━━━━━━━━━━━━━⚡ OP4 ✅✅(nova)🚨100%
🟢 M3U: http://x25.axpl.shop/get.php?username=${user}&password=${password}&type=m3u_plus&output=mpegts

🟡 HLS OP2:  http://x25.axpl.shop/get.php?username=${user}&password=${password}&type=m3u_plus&output=hls

━━━━━━━━━━━━━━━━━━━⚡

🎥 WEB PLAYER:
📺http://webtv.iptvblinkplayer.com/xtream-code

━━━━━━━━━━━━━━━━━━━⚡
📡 Configuração de IP STB OTT

🔹 IP do Servidor: *************

Como configurar?

1️⃣ Acesse as configurações de rede da sua televisão.
2️⃣ Insira o IP informado acima.
3️⃣ Abra o aplicativo e escaneie o QR Code com seu celular.
4️⃣ No celular, insira:

KEY DO SERVIDOR: 18 e 19

Usuário e senha do servidor.
5️⃣ Confirme e pronto! Seu serviço já estará funcionando.

🔹 Aproveite e bom uso!

✅ ACESSO XC:
📺 NAME: CINE FLIX
🌏 URL: http://cdn.dns7b.site
🟢 URL2:http://cdn.mp77.site
👤 Usuário: ${user}
🔐 Senha: ${password}
📶 Máximo de conexões: 1

✅ ACESSO SMARTERS:
📺 NAME: CINE FLIX
👤 Usuário: ${user}
🔐 Senha: ${password}
🌏 URL: http://cdn.dns7b.site
🌏 URL2: http://cdn.mp77.site
📶 Máximo de conexões: 1

✅ ACESSO XCLOUD:
✅ PROVIDER: LEGACY2
✅ Usuário: ${user}
✅ Senha: ${password}
📶 Máximo de conexões: 1

━━━━━━━━━━━━━━━━━━
━━━━━━━━━━━━━━━━━━━⚡

📺 Para TVs Android – XCLOUD
🔗 Link Direto: http://aftv.news/7426653
📥 Código Downloader: 7426653

📱 Para Celulares Android – XCLOUD
🔗 Link Direto: http://aftv.news/8488260
📥 Código Downloader: 8488260

🤖 Cine Flix Smart
🔗 Link: aftv.news/3364394
🟢 Código downloader: 3364394

📈 IBOV Cine Flix
🔗 Link: aftv.news/3644594
🟢 Código downloader: 3644594

👨‍💻 CINE IBO
🔗 Link do app: https://aftv.news/161024
🔑 Código Downloader: 161024

👨‍💻 CINE FLIX SMART V8
🔗 Link do app: https://aftv.news/818498
🔑 Código Downloader: 818498

👨‍💻 CINE FLIX XC NET FLIX
🔗 Link do app: https://aftv.news/9137664
🔑 Código Downloader: 9137664

━━━━━━━━━━━━━━━━━━━

✅ Loja de aplicativos Cine BR
🛑 Novos apps Cine Flix disponíveis na loja Cine Flix BR
📍 Loja de aplicativos:
🔗 https://lojacinebr.in/
📁 Código de acesso: 3006

━━━━━━━━━━━━━━━━━━━
━━━━━━━━━━━━━━━━━━━

✍ Att: EQUIPE CINE FLIX 🧙🏼‍♂️🐺`;

  // Formato compatível com a interface TestResponse
  const testData = {
    success: true,
    message: "Teste liberado com sucesso! Acesso válido por 24 horas.",
    testUrl: "http://cdn.dns7b.site",
    credentials: {
      username: user.toString(),
      password: password.toString()
    },
    accessDetails: {
      code: user.toString(),
      dnsStb: "cdn.dns7b.site",
      urlXciptv: [`http://cdn.dns7b.site/get.php?username=${user}&password=${password}&type=m3u_plus&output=mpegts`],
      linkM3u: `http://cdn.dns7b.site/get.php?username=${user}&password=${password}&type=m3u_plus&output=mpegts`,
      linkM3uShort: `http://e.cdn.dns7b.site/p/${user}/${password}/m3u`,
      linkHls: `http://cdn.dns7b.site/get.php?username=${user}&password=${password}&type=m3u_plus&output=hls`,
      linkHlsShort: `http://e.cdn.dns7b.site/p/${user}/${password}/hls`,
      linkSsiptv: `http://e.cdn.dns7b.site/p/${user}/${password}/ssiptv`,
      webPlayers: [`http://webtv.iptvblinkplayer.com/xtream-code`],
      iptvStream: `http://cdn.dns7b.site/live/${user}/${password}`,
      expiresAt: expiresDate,
      connections: 1,
      planName: "👽TESTE IPTV S/ ADULTOS 👽 PRO",
      price: "R$ 0,00",
      createdAt: createdDate,
      renewalUrl: "https://cineflx.topbrr.shop/#/checkout/RXDgQzY1ex/MeWew2A8Ln"
    },
    rawResponse: fullFormattedResponse,
    // Manter compatibilidade com o formato antigo
    user: user.toString(),
    password: password.toString(),
    expirationDate: expiresDate,
    urls: {
      m3u: `http://cdn.dns7b.site/get.php?username=${user}&password=${password}&type=m3u_plus&output=mpegts`,
      hls: `http://cdn.dns7b.site/get.php?username=${user}&password=${password}&type=m3u_plus&output=hls`,
      short_m3u: `http://e.cdn.dns7b.site/p/${user}/${password}/m3u`,
      short_hls: `http://e.cdn.dns7b.site/p/${user}/${password}/hls`,
      ssiptv: `http://e.cdn.dns7b.site/p/${user}/${password}/ssiptv`
    },
    server: {
      host: 'cdn.dns7b.site',
      port: '80'
    }
  };

  console.log('✅ Teste gerado:', testData);
  res.json(testData);
});

// Rota de pagamento PIX
app.post('/payment/pix', (req, res) => {
  console.log('💰 Gerando PIX:', req.body);

  const { amount, description, customerName, planType } = req.body;

  // Gerar um código PIX simulado mais realista
  const pixCode = `00020126580014br.gov.bcb.pix0136${Math.random().toString(36).substr(2, 32)}520400005303986540${amount.toFixed(2)}5802BR5925${customerName || 'SmartV IPTV'}6009SAO PAULO62070503***6304${Math.random().toString(36).substr(2, 4).toUpperCase()}`;

  // Gerar uma imagem de QR Code placeholder mais realista (200x200 pixels)
  const qrCodeBase64 = generateQRCodePlaceholder();

  // Simular resposta do Mercado Pago no formato correto
  const pixData = {
    id: Math.random().toString(36).substr(2, 9),
    status: 'pending',
    qrCode: pixCode,
    qrCodeUrl: `data:image/png;base64,${qrCodeBase64}`,
    pixKey: pixCode,
    amount: amount,
    transactionAmount: amount,
    description: description || `SmartV IPTV - ${planType || 'Plano Selecionado'}`,
    expiresAt: new Date(Date.now() + 15 * 60 * 1000).toISOString() // 15 minutos
  };

  console.log('✅ PIX gerado:', pixData);
  res.json(pixData);
});

// Rota de health check
app.get('/health', (req, res) => {
  res.json({ status: 'ok', timestamp: new Date().toISOString() });
});

// Proxy para o Encore backend (fallback)
app.use('/', createProxyMiddleware({
  target: 'http://localhost:4000',
  changeOrigin: true,
  logLevel: 'debug',
  onError: (err, req, res) => {
    console.error('Proxy error:', err);
    res.status(500).json({ error: 'Backend não disponível' });
  },
  onProxyReq: (proxyReq, req, res) => {
    console.log('Proxying request to:', proxyReq.path);
  }
}));

const server = app.listen(PORT, '0.0.0.0', () => {
  console.log(`🚀 Proxy server rodando em http://0.0.0.0:${PORT}`);
  console.log(`📱 Acesse do mobile: http://***********:${PORT}`);
  console.log(`🔗 Proxying para: http://localhost:4000`);
  console.log('✅ Servidor iniciado com sucesso!');
});

server.on('error', (err) => {
  console.error('❌ Erro no servidor:', err);
});

// Manter o processo vivo
process.on('SIGINT', () => {
  console.log('\n👋 Proxy server sendo encerrado...');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n👋 Proxy server sendo encerrado...');
  process.exit(0);
});
