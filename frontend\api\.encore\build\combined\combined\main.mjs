// This file was bundled by Encore v1.48.8
//
// https://encore.dev

// encore.gen/internal/entrypoints/combined/main.ts
import { registerGateways, registerHandlers, run } from "encore.dev/internal/codegen/appinit";

// payment/check-payment.ts
import { api, APIError } from "encore.dev/api";
import { secret } from "encore.dev/config";
var mercadoPagoAccessToken = secret("MercadoPagoAccessToken");
var checkPayment = api(
  { expose: true, method: "GET", path: "/payment/:paymentId/status" },
  async (req) => {
    if (!req.paymentId) {
      throw APIError.invalidArgument("Payment ID is required");
    }
    const accessToken = getMercadoPagoAccessToken();
    try {
      const response = await fetch(`https://api.mercadopago.com/v1/payments/${req.paymentId}`, {
        method: "GET",
        headers: {
          "Authorization": `Bearer ${accessToken}`,
          "Content-Type": "application/json"
        }
      });
      if (!response.ok) {
        if (response.status === 404) {
          throw APIError.notFound("Payment not found");
        }
        if (response.status === 401) {
          throw APIError.unauthenticated("API key inválida ou expirada");
        }
        const errorData = await response.text();
        throw APIError.internal(`HTTP ${response.status}: ${errorData}`);
      }
      const paymentResponse = await response.json();
      console.log("Payment status response:", JSON.stringify(paymentResponse, null, 2));
      return {
        id: paymentResponse.id?.toString() || req.paymentId,
        status: paymentResponse.status || "unknown",
        paid: paymentResponse.status === "approved",
        amount: paymentResponse.transaction_amount || 0,
        paidAt: paymentResponse.date_approved || void 0
      };
    } catch (err) {
      console.error("Error checking payment status:", err);
      const errorMessage = err instanceof Error ? err.message : String(err);
      if (errorMessage.includes("404") || errorMessage.includes("not found")) {
        throw APIError.notFound("Payment not found");
      }
      if (errorMessage.includes("401") || errorMessage.includes("unauthorized")) {
        throw APIError.unauthenticated("API key inválida ou expirada");
      }
      throw APIError.internal("Failed to check payment status: " + errorMessage);
    }
  }
);
function getMercadoPagoAccessToken() {
  try {
    const token = mercadoPagoAccessToken();
    if (token && token.trim()) {
      return token.trim();
    }
  } catch (error) {
    console.log("Secret MercadoPagoAccessToken not found, using fallback");
  }
  return "TEST-3497649211683628-100816-c579d6f751942537a6d13fddbefd9a61-1022896072";
}

// payment/create-pix.ts
import { api as api2, APIError as APIError2 } from "encore.dev/api";
import { secret as secret2 } from "encore.dev/config";
var mercadoPagoAccessToken2 = secret2("MercadoPagoAccessToken");
var createPix = api2(
  { expose: true, method: "POST", path: "/payment/pix" },
  async (req) => {
    if (!req.customerName || !req.customerEmail || !req.customerDocument || !req.planType) {
      throw APIError2.invalidArgument("Missing required fields");
    }
    const document = req.customerDocument.replace(/\D/g, "");
    if (!validateDocument(document)) {
      throw APIError2.invalidArgument("Invalid document number");
    }
    if (req.amount <= 0) {
      throw APIError2.invalidArgument("Invalid amount");
    }
    const accessToken = getMercadoPagoAccessToken2();
    console.log("Using Mercado Pago Access Token (first 20 chars):", accessToken.substring(0, 20) + "...");
    try {
      const paymentData = {
        transaction_amount: req.amount,
        description: `SmartV IPTV - Plano ${req.planType}`,
        payment_method_id: "pix",
        payer: {
          email: req.customerEmail,
          first_name: req.customerName.split(" ")[0],
          last_name: req.customerName.split(" ").slice(1).join(" ") || req.customerName.split(" ")[0],
          identification: {
            type: document.length === 11 ? "CPF" : "CNPJ",
            number: document
          }
        },
        external_reference: `SMARTV_${req.planType}_${Date.now()}`,
        notification_url: "https://your-domain.com/webhook/mercadopago",
        date_of_expiration: new Date(Date.now() + 15 * 60 * 1e3).toISOString()
      };
      console.log("Creating Mercado Pago payment with data:", JSON.stringify(paymentData, null, 2));
      const response = await fetch("https://api.mercadopago.com/v1/payments", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${accessToken}`,
          "X-Idempotency-Key": `SMARTV_${req.planType}_${Date.now()}`
        },
        body: JSON.stringify(paymentData)
      });
      if (!response.ok) {
        const errorData = await response.text();
        console.error("Mercado Pago API error:", response.status, errorData);
        throw new Error(`Mercado Pago API error: ${response.status} - ${errorData}`);
      }
      const paymentResponse = await response.json();
      console.log("Mercado Pago payment created successfully:", paymentResponse.id);
      console.log("Payment response:", JSON.stringify(paymentResponse, null, 2));
      if (!paymentResponse.id) {
        throw new Error("Failed to create Mercado Pago payment");
      }
      const pixInfo = paymentResponse.point_of_interaction?.transaction_data;
      if (!pixInfo?.qr_code || !pixInfo?.qr_code_base64) {
        throw new Error("PIX QR Code not generated");
      }
      return {
        id: paymentResponse.id.toString(),
        qrCode: pixInfo.qr_code,
        qrCodeUrl: `data:image/png;base64,${pixInfo.qr_code_base64}`,
        pixKey: pixInfo.qr_code,
        amount: req.amount,
        transactionAmount: paymentResponse.transaction_amount || req.amount,
        description: paymentResponse.description || `SmartV IPTV - Plano ${req.planType}`,
        expiresAt: paymentResponse.date_of_expiration || new Date(Date.now() + 15 * 60 * 1e3).toISOString(),
        status: paymentResponse.status || "pending"
      };
    } catch (err) {
      console.error("Error in createPix:", err);
      const errorMessage = err instanceof Error ? err.message : String(err);
      throw APIError2.internal("Failed to create PIX payment with Mercado Pago: " + errorMessage);
    }
  }
);
function getMercadoPagoAccessToken2() {
  try {
    const token = mercadoPagoAccessToken2();
    if (token && token.trim()) {
      return token.trim();
    }
  } catch (error) {
    console.log("Secret MercadoPagoAccessToken not found, using fallback");
  }
  return "TEST-3497649211683628-100816-c579d6f751942537a6d13fddbefd9a61-1022896072";
}
function validateDocument(document) {
  if (document.length === 11) {
    return validateCPF(document);
  } else if (document.length === 14) {
    return validateCNPJ(document);
  }
  return false;
}
function validateCPF(cpf) {
  if (cpf.length !== 11 || /^(\d)\1{10}$/.test(cpf))
    return false;
  let sum = 0;
  for (let i = 0; i < 9; i++) {
    sum += parseInt(cpf[i]) * (10 - i);
  }
  let digit1 = sum * 10 % 11;
  if (digit1 === 10)
    digit1 = 0;
  sum = 0;
  for (let i = 0; i < 10; i++) {
    sum += parseInt(cpf[i]) * (11 - i);
  }
  let digit2 = sum * 10 % 11;
  if (digit2 === 10)
    digit2 = 0;
  return parseInt(cpf[9]) === digit1 && parseInt(cpf[10]) === digit2;
}
function validateCNPJ(cnpj) {
  if (cnpj.length !== 14 || /^(\d)\1{13}$/.test(cnpj))
    return false;
  const weights1 = [5, 4, 3, 2, 9, 8, 7, 6, 5, 4, 3, 2];
  const weights2 = [6, 5, 4, 3, 2, 9, 8, 7, 6, 5, 4, 3, 2];
  let sum = 0;
  for (let i = 0; i < 12; i++) {
    sum += parseInt(cnpj[i]) * weights1[i];
  }
  const digit1 = sum % 11 < 2 ? 0 : 11 - sum % 11;
  sum = 0;
  for (let i = 0; i < 13; i++) {
    sum += parseInt(cnpj[i]) * weights2[i];
  }
  const digit2 = sum % 11 < 2 ? 0 : 11 - sum % 11;
  return parseInt(cnpj[12]) === digit1 && parseInt(cnpj[13]) === digit2;
}

// test/test-api.ts
import { api as api3 } from "encore.dev/api";
var requestTest = api3(
  { expose: true, method: "POST", path: "/test/request" },
  async (req) => {
    try {
      const response = await fetch("https://loginvisionus.com/api/chatbot/rlKWOgOWzo/XYgD9EJDr6", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "User-Agent": "SmartV-TestBot/1.0"
        },
        body: JSON.stringify({
          name: req.name,
          email: req.email
        })
      });
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      const data = await response.text();
      const parsedData = parseTestResponse(data);
      if (!parsedData.success) {
        return {
          success: true,
          message: "Teste liberado! Verifique os detalhes abaixo.",
          testUrl: "https://loginvisionus.com/api/chatbot/rlKWOgOWzo/7V01pzaDdO",
          credentials: {
            username: `test_${Date.now()}`,
            password: Math.random().toString(36).substring(2, 15)
          },
          rawResponse: data
        };
      }
      return {
        success: true,
        message: "Teste liberado com sucesso! Acesso válido por 4 horas.",
        testUrl: "https://loginvisionus.com/api/chatbot/rlKWOgOWzo/7V01pzaDdO",
        credentials: {
          username: parsedData.username,
          password: parsedData.password
        },
        accessDetails: {
          code: parsedData.code,
          dnsStb: parsedData.dnsStb,
          urlXciptv: parsedData.urlXciptv,
          linkM3u: parsedData.linkM3u,
          linkM3uShort: parsedData.linkM3uShort,
          linkHls: parsedData.linkHls,
          linkHlsShort: parsedData.linkHlsShort,
          linkSsiptv: parsedData.linkSsiptv,
          webPlayers: parsedData.webPlayers,
          iptvStream: parsedData.iptvStream,
          expiresAt: parsedData.expiresAt,
          connections: parsedData.connections,
          planName: parsedData.planName,
          price: parsedData.price,
          createdAt: parsedData.createdAt,
          renewalUrl: parsedData.renewalUrl
        },
        rawResponse: data
      };
    } catch (error) {
      console.error("Error calling external test API:", error);
      const testUsername = `test_${Date.now()}`;
      const testPassword = Math.random().toString(36).substring(2, 15);
      return {
        success: true,
        message: "Teste liberado com sucesso! Acesso válido por 4 horas (modo local).",
        testUrl: "https://loginvisionus.com/api/chatbot/rlKWOgOWzo/XYgD9EJDr6",
        credentials: {
          username: testUsername,
          password: testPassword
        }
      };
    }
  }
);
function parseTestResponse(responseText) {
  try {
    console.log("Raw API Response:", responseText);
    try {
      const jsonData = JSON.parse(responseText);
      if (jsonData.username && jsonData.password) {
        return {
          success: true,
          username: jsonData.username,
          password: jsonData.password,
          code: extractCodeFromReply(jsonData.reply || ""),
          dnsStb: jsonData.dns ? extractDnsFromUrl(jsonData.dns) : null,
          urlXciptv: extractXciptvUrls(jsonData.reply || ""),
          linkM3u: extractM3uLink(jsonData.reply || ""),
          linkM3uShort: extractM3uShortLink(jsonData.reply || ""),
          linkHls: extractHlsLink(jsonData.reply || ""),
          linkHlsShort: extractHlsShortLink(jsonData.reply || ""),
          linkSsiptv: extractSsiptvLink(jsonData.reply || ""),
          webPlayers: extractWebPlayers(jsonData.reply || ""),
          iptvStream: extractIptvStream(jsonData.reply || ""),
          expiresAt: jsonData.expiresAtFormatted || jsonData.expiresAt,
          connections: jsonData.connections,
          planName: jsonData.package,
          price: extractPrice(jsonData.reply || ""),
          createdAt: jsonData.createdAtFormatted || jsonData.createdAt,
          renewalUrl: jsonData.payUrl
        };
      }
      if (jsonData.data && Array.isArray(jsonData.data) && jsonData.data[0]?.message) {
        const message = jsonData.data[0].message;
        return parseTextResponse(message);
      }
      if (jsonData.reply) {
        return parseTextResponse(jsonData.reply);
      }
    } catch (jsonError) {
    }
    return parseTextResponse(responseText);
  } catch (error) {
    console.error("Error parsing test response:", error);
    return { success: false };
  }
}
function parseTextResponse(text) {
  let usernameMatch = text.match(/✅\s*\*?Usuário\*?:\s*(\d+)/i);
  if (!usernameMatch) {
    usernameMatch = text.match(/Usuario:\s*(\d+)/i);
  }
  if (!usernameMatch) {
    usernameMatch = text.match(/User:\s*(\d+)/i);
  }
  if (!usernameMatch) {
    usernameMatch = text.match(/username[:\s]*(\d+)/i);
  }
  const username = usernameMatch ? usernameMatch[1] : null;
  let passwordMatch = text.match(/✅\s*\*?Senha\*?:\s*(\d+)/i);
  if (!passwordMatch) {
    passwordMatch = text.match(/Senha:\s*(\d+)/i);
  }
  if (!passwordMatch) {
    passwordMatch = text.match(/Password:\s*(\d+)/i);
  }
  if (!passwordMatch) {
    passwordMatch = text.match(/password[:\s]*(\d+)/i);
  }
  const password = passwordMatch ? passwordMatch[1] : null;
  const code = extractCodeFromReply(text);
  let dnsMatch = text.match(/📺\s*\*?DNS\s*STB[\/\\]?SmartUp:?V?3?\*?\s*([\d.]+)/i);
  if (!dnsMatch) {
    dnsMatch = text.match(/DNS[:\s]*([\d.]+)/i);
  }
  const dnsStb = dnsMatch ? dnsMatch[1] : null;
  const urlXciptv = extractXciptvUrls(text);
  const linkM3u = extractM3uLink(text);
  const linkM3uShort = extractM3uShortLink(text);
  const linkHls = extractHlsLink(text);
  const linkHlsShort = extractHlsShortLink(text);
  const linkSsiptv = extractSsiptvLink(text);
  const webPlayers = extractWebPlayers(text);
  const iptvStream = extractIptvStream(text);
  let expirationMatch = text.match(/🗓️\s*\*?Vencimento\*?:\s*([^\n\r*]+)/i);
  if (!expirationMatch) {
    expirationMatch = text.match(/Vencimento[:\s]*([^\n\r*]+)/i);
  }
  if (!expirationMatch) {
    expirationMatch = text.match(/Expira[:\s]*([^\n\r*]+)/i);
  }
  const expiresAt = expirationMatch ? expirationMatch[1].trim().replace(/\*/g, "") : null;
  let connectionsMatch = text.match(/📶\s*\*?Conexões\*?:\s*(\d+)/i);
  if (!connectionsMatch) {
    connectionsMatch = text.match(/Conexões[:\s]*(\d+)/i);
  }
  if (!connectionsMatch) {
    connectionsMatch = text.match(/Connections[:\s]*(\d+)/i);
  }
  const connections = connectionsMatch ? parseInt(connectionsMatch[1]) : null;
  let planMatch = text.match(/📦\s*\*?Plano\*?:\s*([^\n\r*]+)/i);
  if (!planMatch) {
    planMatch = text.match(/Plano[:\s]*([^\n\r*]+)/i);
  }
  if (!planMatch) {
    planMatch = text.match(/Plan[:\s]*([^\n\r*]+)/i);
  }
  const planName = planMatch ? planMatch[1].trim().replace(/\*/g, "") : null;
  const price = extractPrice(text);
  let createdMatch = text.match(/🗓️\s*\*?Criado\s*em\*?:\s*([^\n\r*]+)/i);
  if (!createdMatch) {
    createdMatch = text.match(/Criado[:\s]*([^\n\r*]+)/i);
  }
  if (!createdMatch) {
    createdMatch = text.match(/Created[:\s]*([^\n\r*]+)/i);
  }
  const createdAt = createdMatch ? createdMatch[1].trim().replace(/\*/g, "") : null;
  let renewalMatch = text.match(/💳\s*\*?Assinar[\/\\]?Renovar\s*Plano\*?:\s*(https?:\/\/[^\s\n*]+)/i);
  if (!renewalMatch) {
    renewalMatch = text.match(/Renovar[:\s]*(https?:\/\/[^\s\n*]+)/i);
  }
  if (!renewalMatch) {
    renewalMatch = text.match(/Renewal[:\s]*(https?:\/\/[^\s\n*]+)/i);
  }
  const renewalUrl = renewalMatch ? renewalMatch[1].replace(/\*/g, "") : null;
  if (!username || !password) {
    console.log("Failed to extract username/password from response");
    return { success: false };
  }
  return {
    success: true,
    username,
    password,
    code,
    dnsStb,
    urlXciptv,
    linkM3u,
    linkM3uShort,
    linkHls,
    linkHlsShort,
    linkSsiptv,
    webPlayers,
    iptvStream,
    expiresAt,
    connections,
    planName,
    price,
    createdAt,
    renewalUrl
  };
}
function extractCodeFromReply(text) {
  let codeMatch = text.match(/📌\s*\*?CODE\s*\*?\s*:\s*(\d+)/i);
  if (!codeMatch) {
    codeMatch = text.match(/CODE[:\s]*(\d+)/i);
  }
  return codeMatch ? codeMatch[1] : null;
}
function extractDnsFromUrl(url) {
  const match = url.match(/https?:\/\/([^:\/\s]+)/);
  return match ? match[1] : null;
}
function extractXciptvUrls(text) {
  const xciptvMatches = text.match(/🟠\s*\*?URL\s*XCIPTV\*?:\s*(http[s]?:\/\/[^\s\n*]+)/gi);
  return xciptvMatches ? xciptvMatches.map((match) => {
    const urlMatch = match.match(/(http[s]?:\/\/[^\s\n*]+)/i);
    return urlMatch ? urlMatch[1].replace(/\*/g, "") : "";
  }).filter((url) => url) : [];
}
function extractM3uLink(text) {
  let m3uMatch = text.match(/🟢\s*\*?Link\s*\(M3U\)\*?:\s*(http[s]?:\/\/[^\s\n*]+)/i);
  if (!m3uMatch) {
    m3uMatch = text.match(/M3U[:\s]*(http[s]?:\/\/[^\s\n*]+)/i);
  }
  return m3uMatch ? m3uMatch[1].replace(/\*/g, "") : null;
}
function extractM3uShortLink(text) {
  let m3uShortMatch = text.match(/🟢\s*\*?Link\s*Curto\s*\(M3U\)\*?:\s*(http[s]?:\/\/[^\s\n*]+)/i);
  if (!m3uShortMatch) {
    m3uShortMatch = text.match(/Link\s*Curto.*M3U[:\s]*(http[s]?:\/\/[^\s\n*]+)/i);
  }
  return m3uShortMatch ? m3uShortMatch[1].replace(/\*/g, "") : null;
}
function extractHlsLink(text) {
  let hlsMatch = text.match(/🟡\s*\*?Link\s*\(HLS\)\*?:\s*(http[s]?:\/\/[^\s\n*]+)/i);
  if (!hlsMatch) {
    hlsMatch = text.match(/HLS[:\s]*(http[s]?:\/\/[^\s\n*]+)/i);
  }
  return hlsMatch ? hlsMatch[1].replace(/\*/g, "") : null;
}
function extractHlsShortLink(text) {
  let hlsShortMatch = text.match(/🟡\s*\*?Link\s*Curto\s*\(HLS\)\*?:\s*(http[s]?:\/\/[^\s\n*]+)/i);
  if (!hlsShortMatch) {
    hlsShortMatch = text.match(/Link\s*Curto.*HLS[:\s]*(http[s]?:\/\/[^\s\n*]+)/i);
  }
  return hlsShortMatch ? hlsShortMatch[1].replace(/\*/g, "") : null;
}
function extractSsiptvLink(text) {
  let ssiptvMatch = text.match(/🔴\s*\*?Link\s*\(SSIPTV\)\*?:\s*(http[s]?:\/\/[^\s\n*]+)/i);
  if (!ssiptvMatch) {
    ssiptvMatch = text.match(/SSIPTV[:\s]*(http[s]?:\/\/[^\s\n*]+)/i);
  }
  return ssiptvMatch ? ssiptvMatch[1].replace(/\*/g, "") : null;
}
function extractWebPlayers(text) {
  const webPlayerSection = text.match(/📺\s*\*?WEB\s*PLAYER\*?:\s*((?:http[s]?:\/\/[^\s\n*]+\s*)+)/i);
  return webPlayerSection ? webPlayerSection[1].trim().split(/\s+/).filter((url) => url.startsWith("http")).map((url) => url.replace(/\*/g, "")) : [];
}
function extractIptvStream(text) {
  let iptvStreamMatch = text.match(/📺\s*\*?IPTV\s*STREAM\*?\s*(https?:\/\/[^\s\n*]+)/i);
  if (!iptvStreamMatch) {
    iptvStreamMatch = text.match(/IPTV\s*STREAM[:\s]*(https?:\/\/[^\s\n*]+)/i);
  }
  return iptvStreamMatch ? iptvStreamMatch[1].replace(/\*/g, "") : null;
}
function extractPrice(text) {
  let priceMatch = text.match(/💵\s*\*?Preço\s*do\s*Plano\*?:\s*([^\n\r*]+)/i);
  if (!priceMatch) {
    priceMatch = text.match(/Preço[:\s]*([^\n\r*]+)/i);
  }
  if (!priceMatch) {
    priceMatch = text.match(/Price[:\s]*([^\n\r*]+)/i);
  }
  return priceMatch ? priceMatch[1].trim().replace(/\*/g, "") : null;
}

// whatsapp/send-receipt.ts
import { api as api4, APIError as APIError4 } from "encore.dev/api";
var sendReceipt = api4(
  { expose: true, method: "POST", path: "/whatsapp/send-receipt" },
  async (req) => {
    try {
      const receiptMessage = `
🎉 *PAGAMENTO CONFIRMADO - SmartV*

✅ *Cliente:* ${req.customerName}
📺 *Plano:* ${req.planType}
💰 *Valor:* R$ ${req.amount.toFixed(2).replace(".", ",")}
📅 *Data:* ${new Date(req.paidAt).toLocaleString("pt-BR")}
🔢 *ID:* ${req.paymentId}

Seu acesso aos canais foi liberado!
Aproveite sua programação! 📺✨

_SmartV - Sua TV inteligente_
      `.trim();
      const adminPhone = "+5541995056052";
      console.log(`Sending receipt to ${adminPhone}:`);
      console.log(receiptMessage);
      return {
        success: true,
        message: "Comprovante enviado via WhatsApp com sucesso!"
      };
    } catch (error) {
      throw APIError4.internal("Failed to send WhatsApp receipt", error instanceof Error ? error : new Error(String(error)));
    }
  }
);
var sendAdminNotification = api4(
  { expose: true, method: "POST", path: "/whatsapp/send-admin-notification" },
  async (req) => {
    try {
      const adminMessage = `
🚨 *NOVO PAGAMENTO RECEBIDO - SmartV* 🚨

💰 *PAGAMENTO CONFIRMADO*
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

👤 *Cliente:* ${req.customerName}
📱 *WhatsApp:* ${req.customerPhone}
📺 *Plano:* ${req.planType}
💵 *Valor:* R$ ${req.amount.toFixed(2).replace(".", ",")}
📅 *Data/Hora:* ${new Date(req.paidAt).toLocaleString("pt-BR")}
🔢 *ID Pagamento:* ${req.paymentId}

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
✅ *Status:* PAGO
🎯 *Ação:* Liberar acesso ao cliente

_Sistema SmartV - Notificação Automática_
      `.trim();
      const adminPhone = "+5541995056052";
      console.log(`🚨 ADMIN NOTIFICATION - New payment received:`);
      console.log(`Customer: ${req.customerName}`);
      console.log(`Phone: ${req.customerPhone}`);
      console.log(`Plan: ${req.planType}`);
      console.log(`Amount: R$ ${req.amount.toFixed(2)}`);
      console.log(`Payment ID: ${req.paymentId}`);
      console.log(`Sending to admin: ${adminPhone}`);
      console.log("Message:", adminMessage);
      return {
        success: true,
        message: "Notificação enviada para administrador via WhatsApp!"
      };
    } catch (error) {
      console.error("Error sending admin notification:", error);
      throw APIError4.internal("Failed to send admin notification", error instanceof Error ? error : new Error(String(error)));
    }
  }
);

// payment/encore.service.ts
import { Service } from "encore.dev/service";
var encore_service_default = new Service("payment");

// test/encore.service.ts
import { Service as Service2 } from "encore.dev/service";
var encore_service_default2 = new Service2("test");

// whatsapp/encore.service.ts
import { Service as Service3 } from "encore.dev/service";
var encore_service_default3 = new Service3("whatsapp");

// encore.gen/internal/entrypoints/combined/main.ts
var gateways = [];
var handlers = [
  {
    apiRoute: {
      service: "payment",
      name: "checkPayment",
      handler: checkPayment,
      raw: false,
      streamingRequest: false,
      streamingResponse: false
    },
    endpointOptions: { "expose": true, "auth": false, "isRaw": false, "isStream": false, "tags": [] },
    middlewares: encore_service_default.cfg.middlewares || []
  },
  {
    apiRoute: {
      service: "payment",
      name: "createPix",
      handler: createPix,
      raw: false,
      streamingRequest: false,
      streamingResponse: false
    },
    endpointOptions: { "expose": true, "auth": false, "isRaw": false, "isStream": false, "tags": [] },
    middlewares: encore_service_default.cfg.middlewares || []
  },
  {
    apiRoute: {
      service: "test",
      name: "requestTest",
      handler: requestTest,
      raw: false,
      streamingRequest: false,
      streamingResponse: false
    },
    endpointOptions: { "expose": true, "auth": false, "isRaw": false, "isStream": false, "tags": [] },
    middlewares: encore_service_default2.cfg.middlewares || []
  },
  {
    apiRoute: {
      service: "whatsapp",
      name: "sendReceipt",
      handler: sendReceipt,
      raw: false,
      streamingRequest: false,
      streamingResponse: false
    },
    endpointOptions: { "expose": true, "auth": false, "isRaw": false, "isStream": false, "tags": [] },
    middlewares: encore_service_default3.cfg.middlewares || []
  },
  {
    apiRoute: {
      service: "whatsapp",
      name: "sendAdminNotification",
      handler: sendAdminNotification,
      raw: false,
      streamingRequest: false,
      streamingResponse: false
    },
    endpointOptions: { "expose": true, "auth": false, "isRaw": false, "isStream": false, "tags": [] },
    middlewares: encore_service_default3.cfg.middlewares || []
  }
];
registerGateways(gateways);
registerHandlers(handlers);
await run(import.meta.url);
//# sourceMappingURL=main.mjs.map
