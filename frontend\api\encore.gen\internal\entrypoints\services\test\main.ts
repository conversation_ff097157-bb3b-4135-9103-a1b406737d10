import { registerHandlers, run, type Handler } from "encore.dev/internal/codegen/appinit";
import { Worker, isMainThread } from "node:worker_threads";
import { fileURLToPath } from "node:url";
import { availableParallelism } from "node:os";

import { requestTest as requestTestImpl0 } from "../../../../../test\\test-api";
import * as test_service from "../../../../../test\\encore.service";

const handlers: Handler[] = [
    {
        apiRoute: {
            service:           "test",
            name:              "requestTest",
            handler:           requestTestImpl0,
            raw:               false,
            streamingRequest:  false,
            streamingResponse: false,
        },
        endpointOptions: {"expose":true,"auth":false,"isRaw":false,"isStream":false,"tags":[]},
        middlewares: test_service.default.cfg.middlewares || [],
    },
];

registerHandlers(handlers);

await run(import.meta.url);
